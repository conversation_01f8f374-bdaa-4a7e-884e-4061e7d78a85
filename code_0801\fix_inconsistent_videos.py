#!/usr/bin/env python3
"""
修复视频处理结果的不一致问题
重新处理在progress.json中标记为已处理但output.csv中Output字段为空的视频
"""

import cv2
import base64
import time
from openai import OpenAI
import os
import pandas as pd
import json
import argparse
from datetime import datetime
import copy

def extract_int(s):
    try:
        # 处理 <qa0> 格式
        if s.startswith('<qa') and '>' in s:
            return int(s[3:s.index('>')])
        # 处理 QA0 格式
        return int(s[0])
    except:
        return None

def load_inconsistent_videos(filename):
    """从文件加载需要重新处理的视频列表"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            videos = [line.strip() for line in f if line.strip()]
        return videos
    except Exception as e:
        print(f"加载视频列表失败: {e}")
        return []

def save_progress(model_name, processed_videos, total_videos):
    """保存处理进度到文件"""
    progress_file = f'code_0801/result/{model_name}_progress.json'
    progress_data = {
        'model': model_name,
        'processed_videos': list(processed_videos),
        'total_videos': total_videos,
        'last_update': datetime.now().isoformat(),
        'completion_rate': len(processed_videos) / total_videos if total_videos > 0 else 0
    }
    
    os.makedirs('code_0801/result', exist_ok=True)
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(progress_data, f, indent=2, ensure_ascii=False)

def load_progress(model_name):
    """从文件加载处理进度"""
    progress_file = f'code_0801/result/{model_name}_progress.json'
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            return set(progress_data.get('processed_videos', []))
        except Exception as e:
            print(f"加载进度文件失败: {e}")
            return set()
    return set()

def fix_inconsistent_videos(model_name, video_list_file, client, folder_path, QA_df):
    """修复指定模型的不一致视频"""
    print(f"\n{'='*50}")
    print(f"开始修复模型: {model_name}")
    print(f"{'='*50}")
    
    # 加载需要重新处理的视频列表
    inconsistent_videos = load_inconsistent_videos(video_list_file)
    if not inconsistent_videos:
        print(f"没有找到需要修复的视频列表")
        return
    
    print(f"需要修复的视频数量: {len(inconsistent_videos)}")
    
    # 数据保存路径
    res_path = f'code_0801/result/{model_name}_trainset_output.csv'
    
    # 加载现有结果DataFrame
    if os.path.exists(res_path):
        res = pd.read_csv(res_path, index_col=0)
        print(f"加载现有结果文件: {res_path}")
    else:
        print(f"错误: 结果文件不存在 {res_path}")
        return
    
    # 加载处理进度
    processed_videos = load_progress(model_name)
    total_videos = len(res['video_id'].unique())
    
    fixed_count = 0
    failed_videos = []
    
    # 遍历需要修复的视频
    for i, select_vid_name in enumerate(inconsistent_videos):
        print(f'\n[{model_name}] 修复视频 {i + 1}/{len(inconsistent_videos)}: {select_vid_name}')
        
        # 检查视频文件是否存在
        video_path = os.path.join(folder_path, str(select_vid_name))
        if not os.path.exists(video_path):
            print(f"警告: 视频文件不存在 {video_path}")
            failed_videos.append(select_vid_name)
            continue
        
        # 检查该视频是否在数据集中
        temp_QA = res[res['video_id'] == select_vid_name]
        if temp_QA.empty:
            print(f"警告: 视频 {select_vid_name} 不在数据集中")
            failed_videos.append(select_vid_name)
            continue

        try:
            video = cv2.VideoCapture(video_path)
            video_fps = video.get(cv2.CAP_PROP_FPS)

            base64Frames = []
            while video.isOpened():
                success, frame = video.read()
                if not success:
                    break
                _, buffer = cv2.imencode(".jpg", frame)
                base64Frames.append(base64.b64encode(buffer).decode("utf-8"))

            video.release()
            print(f"读取帧数: {len(base64Frames)}")

            if len(base64Frames) == 0:
                print(f"警告: 视频 {select_vid_name} 无法读取帧")
                failed_videos.append(select_vid_name)
                continue

            filter_base64Frames = base64Frames

            prompt = "Please assume the role of an agent. The video represents your egocentric observations from the past to the present. Please answer the following questions: \n"

            for temp_QA_idx in range(temp_QA.shape[0]):
                qa = temp_QA['question'].iloc[temp_QA_idx]
                prompt += "<QA%d: %s> \n" % (temp_QA_idx, qa)

            prompt += "The template for the answer is: \
                            <QA0: Think: []; Option: []. \n QA1: Think: []; Option: []. \n QA2: Think: []; Option: []. \n QA3: ...>\n\
                            where the Think explains why you choose this option and your thinking process. The Option only outputs one option from 'A' to 'E' here, do not output redundant content. "

            base64Frames_selected = filter_base64Frames

            video_content = []
            for buffer in base64Frames_selected:
                video_content.append(f"data:image/jpeg;base64,{buffer}")
            content = [
                {
                    "type": "video",
                    "video": video_content
                },
                {
                    "type": "text",
                    "text": prompt
                }
            ]

            PROMPT_MESSAGES = [
                {
                    "role": "user",
                    "content": content
                }
            ]
            
            print("正在调用API...")
            result = client.chat.completions.create(
                model=model_name,
                messages=PROMPT_MESSAGES
            )
            print("API调用成功")
            res_str = result.choices[0].message.content
            print(f"API返回内容: {res_str[:200]}...")

            # 解析结果并更新DataFrame - 处理新的格式
            # 先尝试按 <qa 分割，如果没有则按 QA 分割
            if '<qa' in res_str.lower():
                lines = res_str.lower().split('<qa')
            else:
                lines = res_str.split('QA')
            temp_QA_copy = temp_QA.copy()

            for line_num in range(len(lines)):
                try:
                    line = lines[line_num]
                    corr_idx = extract_int(line)
                    if isinstance(corr_idx, int):
                        temp_QA_copy['Output'].iloc[corr_idx] = line
                except Exception as e:
                    print(f"解析结果时出错: {e}")
                    continue

            # 更新主DataFrame
            res.loc[res['video_id'] == select_vid_name] = temp_QA_copy
            
            # 保存结果
            res.to_csv(res_path)
            fixed_count += 1
            print(f"视频 {select_vid_name} 修复成功")
            
            # 短暂延迟避免API限制
            time.sleep(1)

        except Exception as e:
            print(f"处理视频 {select_vid_name} 时发生错误: {e}")
            failed_videos.append(select_vid_name)
            time.sleep(5)  # 出错时等待更长时间
            continue

    print(f"\n{'='*50}")
    print(f"修复完成统计:")
    print(f"成功修复: {fixed_count} 个视频")
    print(f"修复失败: {len(failed_videos)} 个视频")
    
    if failed_videos:
        failed_file = f"failed_{model_name.replace('-', '_')}_videos.txt"
        with open(failed_file, 'w', encoding='utf-8') as f:
            for video in failed_videos:
                f.write(f"{video}\n")
        print(f"失败视频列表已保存到: {failed_file}")
    
    print(f"结果已保存至: {res_path}")

def main():
    parser = argparse.ArgumentParser(description='修复视频处理结果的不一致问题')
    parser.add_argument('--model', required=True, 
                       choices=['qwen2.5-vl-7b-instruct', 'qwen2.5-vl-72b-instruct', 'qwen2.5-vl-3b-instruct'],
                       help='要修复的模型名称')
    parser.add_argument('--video-list', required=True, help='包含需要修复视频列表的文件路径')
    
    args = parser.parse_args()
    
    # 初始化OpenAI客户端
    client = OpenAI(
        api_key="sk-48dc15715aa94943958ebced812f6fa0",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    # 数据路径
    folder_path = r'E:\Thu实习\code_0801\video_keyframes_min4f'
    QA_df = pd.read_json(r'E:\Thu实习\code_0801\train_set_option_shuffle.json')

    print(f"开始修复模型: {args.model}")
    print(f"视频列表文件: {args.video_list}")
    
    fix_inconsistent_videos(args.model, args.video_list, client, folder_path, QA_df)

if __name__ == '__main__':
    main()
