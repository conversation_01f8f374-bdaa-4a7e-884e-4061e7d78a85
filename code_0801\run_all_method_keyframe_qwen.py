import cv2  # We're using OpenCV to read video, to install !pip install opencv-python
import base64
import time
from openai import OpenAI
from openai import AzureOpenAI
import os
import requests
import re
import pandas as pd
import numpy as np
import warnings
import math
from natsort import natsorted
import copy
import argparse  # 导入 argparse 模块
import json
from datetime import datetime

warnings.filterwarnings("ignore")

def sort_key(file_name):
    # 提取文件名中的数字部分用于排序
    return int(re.search(r'\d+', file_name).group())

def list_files_in_directory(directory):
    try:
        # 获取目录中的所有条目，包括文件和子目录
        entries = os.listdir(directory)
        # 过滤出仅文件
        files = [entry for entry in entries if os.path.isfile(os.path.join(directory, entry))]
        return files
    except FileNotFoundError:
        return f"Directory {directory} not found."
    except Exception as e:
        return str(e)


def extract_frames_from_video(video_path):
    # 打开视频文件
    cap = cv2.VideoCapture(video_path)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    # 计算需要抽取的帧的位置
    frame_indices = [0, total_frames // 3, 2 * total_frames // 3, total_frames - 1]

    frames = []
    for idx in frame_indices:
        cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
        ret, frame = cap.read()
        if ret:
            frames.append(frame)

    cap.release()
    return frames


def get_sorted_file_list(directory, extension=".avi"):
    files = [f for f in os.listdir(directory) if f.endswith(extension)]
    # 使用正则表达式提取文件名中的数字进行排序
    files_sorted = natsorted(files, key=lambda x: int(re.search(r'\d+', x).group()))
    return files_sorted


def collect_frames_from_folder(folder_path):
    avi_files = get_sorted_file_list(folder_path)
    collected_frames = []

    for avi_file in avi_files:
        video_path = os.path.join(folder_path, avi_file)
        frames = extract_frames_from_video(video_path)
        # 只收集前三帧
        collected_frames.extend(frames[:3])

    return collected_frames

def extract_int(s):
    try:
        return int(s[0])
    except:
        return None

def replace_category(x):
    # 检查条件
    if x[:3] == 'Emb' and x.count('_') == 2 and x[-6:-4] == '_3':
        # 执行替换
        x = x[:-6] + x[-4:]
    return x

def save_progress(model_name, processed_videos, total_videos):
    """保存处理进度到文件"""
    progress_file = f'result/{model_name}_progress.json'
    progress_data = {
        'model': model_name,
        'processed_videos': list(processed_videos),
        'total_videos': total_videos,
        'last_update': datetime.now().isoformat(),
        'completion_rate': len(processed_videos) / total_videos if total_videos > 0 else 0
    }
    
    os.makedirs('result', exist_ok=True)
    with open(progress_file, 'w', encoding='utf-8') as f:
        json.dump(progress_data, f, indent=2, ensure_ascii=False)

def load_progress(model_name):
    """从文件加载处理进度"""
    progress_file = f'result/{model_name}_progress.json'
    if os.path.exists(progress_file):
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                progress_data = json.load(f)
            return set(progress_data.get('processed_videos', []))
        except Exception as e:
            print(f"加载进度文件失败: {e}")
            return set()
    return set()

def process_single_model(model_name, client, folder_path, QA_df):
    """处理单个模型的视频问答任务"""
    print(f"\n{'='*50}")
    print(f"开始处理模型: {model_name}")
    print(f"{'='*50}")
    
    # 数据保存路径
    res_path = f'result/{model_name}_trainset_output.csv'
    
    # 加载或创建结果DataFrame
    if os.path.exists(res_path):
        res = pd.read_csv(res_path, index_col=0)
        print(f"加载现有结果文件: {res_path}")
    else:
        res = copy.deepcopy(QA_df)
        res['Output'] = None
        print(f"创建新结果文件: {res_path}")
    
    # 加载处理进度
    processed_videos = load_progress(model_name)
    video_names = res['video_id'].unique()
    total_videos = len(video_names)
    
    print(f"总视频数: {total_videos}")
    print(f"已处理视频数: {len(processed_videos)}")
    print(f"剩余视频数: {total_videos - len(processed_videos)}")
    
    if len(processed_videos) == total_videos:
        print(f"模型 {model_name} 已完成所有视频处理！")
        return
    
    cur_vid_num = len(processed_videos)
    
    # 遍历每个视频
    for select_vid_name in video_names:
        # 跳过已处理的视频
        if select_vid_name in processed_videos:
            continue
            
        print(f'\n[{model_name}] 处理视频 {cur_vid_num + 1}/{total_videos}: {select_vid_name}')
        cur_vid_num += 1

        temp_QA = res[res['video_id']==select_vid_name]

        video = cv2.VideoCapture(os.path.join(folder_path, str(select_vid_name)))
        video_fps = video.get(cv2.CAP_PROP_FPS)

        base64Frames = []
        while video.isOpened():
            success, frame = video.read()
            if not success:
                break
            _, buffer = cv2.imencode(".jpg", frame)
            base64Frames.append(base64.b64encode(buffer).decode("utf-8"))

        video.release()
        print(f"读取帧数: {len(base64Frames)}")

        filter_base64Frames = base64Frames

        prompt = "Please assume the role of an agent. The video represents your egocentric observations from the past to the present. Please answer the following questions: \n"

        for temp_QA_idx in range(temp_QA.shape[0]):
            qa = temp_QA['question'].iloc[temp_QA_idx]
            prompt += "<QA%d: %s> \n" % (temp_QA_idx, qa)

        prompt += "The template for the answer is: \
                        <QA0: Think: []; Option: []. \n QA1: Think: []; Option: []. \n QA2: Think: []; Option: []. \n QA3: ...>\n\
                        where the Think explains why you choose this option and your thinking process. The Option only outputs one option from 'A' to 'E' here, do not output redundant content. "

        try:
            base64Frames_selected = filter_base64Frames

            video_content = []
            for buffer in base64Frames_selected:
                video_content.append(f"data:image/jpeg;base64,{buffer}")
            content = [
                {
                    "type": "video",
                    "video": video_content
                },
                {
                    "type": "text",
                    "text": prompt
                }
            ]

            PROMPT_MESSAGES = [
                {
                    "role": "user",
                    "content": content
                }
            ]
            result = client.chat.completions.create(
                model=model_name,
                messages=PROMPT_MESSAGES
            )
            print("API调用成功")
            res_str = result.choices[0].message.content

            lines = res_str.split('QA')
            for line_num in range(len(lines)):
                try:
                    line = lines[line_num]
                    corr_idx = extract_int(line)
                    if isinstance(corr_idx, int):
                        temp_QA['Output'].iloc[corr_idx] = line
                except:
                    continue
            res.loc[res['video_id'] == select_vid_name] = temp_QA
            
            # 标记视频为已处理
            processed_videos.add(select_vid_name)
            
            # 保存结果和进度
            res.to_csv(res_path)
            save_progress(model_name, processed_videos, total_videos)
            print(f"进度已保存: {len(processed_videos)}/{total_videos}")

        except Exception as e:
            print(f"处理视频 {select_vid_name} 时发生错误: {e}")
            time.sleep(60)
            continue

    print(f"\n模型 {model_name} 处理完成！")
    print(f"结果保存至: {res_path}")

if __name__ == '__main__':
    # 定义要处理的模型列表
    models = [
        "qwen2.5-vl-3b-instruct",
        "qwen2.5-vl-7b-instruct", 
        "qwen2.5-vl-72b-instruct"
    ]

    client = OpenAI(
        # api_key='sk-24d1eb151364436098ac65bc9f460836',
        api_key="sk-48dc15715aa94943958ebced812f6fa0",
        base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
    )

    # 数据读取
    folder_path = r'E:\\Thu实习\\code_0801\\video_keyframes_min4f'
    QA_df = pd.read_json(r'E:\\Thu实习\\code_0801\\train_set_option_shuffle.json')

    print(f"开始批量处理 {len(models)} 个模型")
    print(f"模型列表: {models}")
    
    # 依次处理每个模型
    for model_name in models:
        try:
            process_single_model(model_name, client, folder_path, QA_df)
        except KeyboardInterrupt:
            print(f"\n用户中断，当前模型 {model_name} 的进度已保存")
            break
        except Exception as e:
            print(f"处理模型 {model_name} 时发生严重错误: {e}")
            continue
    
    print("\n所有模型处理完成！")
