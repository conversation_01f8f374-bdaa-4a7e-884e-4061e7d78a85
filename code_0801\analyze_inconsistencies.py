#!/usr/bin/env python3
"""
分析视频处理结果的不一致问题
检查progress.json中标记为已处理但output.csv中Output字段为空的视频
"""

import json
import pandas as pd
import os
from typing import Dict, List, Set

def load_progress_json(filepath: str) -> Set[str]:
    """加载progress.json文件并返回已处理视频集合"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return set(data.get('processed_videos', []))
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return set()

def load_output_csv(filepath: str) -> Dict[str, bool]:
    """加载output.csv文件并返回视频ID到是否有输出的映射"""
    try:
        df = pd.read_csv(filepath)
        # 检查Output列是否为空
        video_output_status = {}
        for _, row in df.iterrows():
            video_id = row['video_id']
            has_output = pd.notna(row['Output']) and str(row['Output']).strip() != ''
            video_output_status[video_id] = has_output
        return video_output_status
    except Exception as e:
        print(f"Error loading {filepath}: {e}")
        return {}

def analyze_model_inconsistencies(model_name: str, result_dir: str) -> Dict:
    """分析单个模型的不一致问题"""
    progress_file = os.path.join(result_dir, f"{model_name}_progress.json")
    output_file = os.path.join(result_dir, f"{model_name}_trainset_output.csv")
    
    print(f"\n=== 分析 {model_name} 模型 ===")
    
    # 加载数据
    processed_videos = load_progress_json(progress_file)
    video_output_status = load_output_csv(output_file)
    
    print(f"Progress.json中已处理视频数量: {len(processed_videos)}")
    print(f"Output.csv中视频数量: {len(video_output_status)}")
    
    # 找出不一致的视频
    inconsistent_videos = []
    for video_id in processed_videos:
        if video_id in video_output_status:
            if not video_output_status[video_id]:
                inconsistent_videos.append(video_id)
        else:
            # 在progress中但不在output中的视频
            inconsistent_videos.append(video_id)
    
    # 统计有输出的视频数量
    videos_with_output = sum(1 for has_output in video_output_status.values() if has_output)
    videos_without_output = len(video_output_status) - videos_with_output
    
    print(f"有Output的视频数量: {videos_with_output}")
    print(f"无Output的视频数量: {videos_without_output}")
    print(f"不一致的视频数量: {len(inconsistent_videos)}")
    
    if inconsistent_videos:
        print(f"需要重新处理的视频 (前10个):")
        for i, video_id in enumerate(inconsistent_videos[:10]):
            print(f"  {i+1}. {video_id}")
        if len(inconsistent_videos) > 10:
            print(f"  ... 还有 {len(inconsistent_videos) - 10} 个视频")
    
    return {
        'model_name': model_name,
        'processed_count': len(processed_videos),
        'output_count': len(video_output_status),
        'videos_with_output': videos_with_output,
        'videos_without_output': videos_without_output,
        'inconsistent_videos': inconsistent_videos,
        'inconsistent_count': len(inconsistent_videos)
    }

def main():
    """主函数"""
    result_dir = "code_0801/result"
    models = [
        "qwen2.5-vl-7b-instruct",
        "qwen2.5-vl-72b-instruct",
        "qwen2.5-vl-3b-instruct"
    ]
    
    print("视频处理结果不一致性分析报告")
    print("=" * 50)
    
    all_results = []
    
    for model in models:
        result = analyze_model_inconsistencies(model, result_dir)
        all_results.append(result)
    
    # 汇总报告
    print(f"\n{'='*50}")
    print("汇总报告")
    print(f"{'='*50}")
    
    total_inconsistent = 0
    priority_order = ["qwen2.5-vl-7b-instruct", "qwen2.5-vl-72b-instruct", "qwen2.5-vl-3b-instruct"]
    
    for model_name in priority_order:
        result = next(r for r in all_results if r['model_name'] == model_name)
        total_inconsistent += result['inconsistent_count']
        priority = priority_order.index(model_name) + 1
        print(f"优先级 {priority}: {model_name}")
        print(f"  - 需要重新处理的视频: {result['inconsistent_count']} 个")
        
        if result['inconsistent_count'] > 0:
            # 保存需要重新处理的视频列表到文件
            output_filename = f"reprocess_{model_name.replace('-', '_')}_videos.txt"
            with open(output_filename, 'w', encoding='utf-8') as f:
                for video_id in result['inconsistent_videos']:
                    f.write(f"{video_id}\n")
            print(f"  - 视频列表已保存到: {output_filename}")
    
    print(f"\n总计需要重新处理的视频: {total_inconsistent} 个")
    
    if total_inconsistent > 0:
        print(f"\n建议的修复顺序:")
        print(f"1. 优先修复 7b 模型 ({next(r for r in all_results if r['model_name'] == 'qwen2.5-vl-7b-instruct')['inconsistent_count']} 个视频)")
        print(f"2. 然后修复 72b 模型 ({next(r for r in all_results if r['model_name'] == 'qwen2.5-vl-72b-instruct')['inconsistent_count']} 个视频)")
        print(f"3. 最后修复 3b 模型 ({next(r for r in all_results if r['model_name'] == 'qwen2.5-vl-3b-instruct')['inconsistent_count']} 个视频)")

if __name__ == "__main__":
    main()
